<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Dashboard</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #0e2433;
            color: white;
            margin: 0;
            padding: 0;
        }

        .header {
            background-color: #45b6fe;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
        }

        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background-color: #0e2433;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
        }

        h2 {
            color: #45b6fe;
        }

        /* Add more styles and components for your dashboard here */
    </style>
</head>
<body>
    <div class="header">
        <h1>USER Dashboard</h1>
    </div>
    <div class="container">
        <h2>Welcome, [[${user.getFullname()}]]</h2>
        
        <span sec:authorize="isAuthenticated">
        <a th:href="@{/logout}">
         Logout
        
        </a>
        
        </span>

    </div>
</body>
</html>
